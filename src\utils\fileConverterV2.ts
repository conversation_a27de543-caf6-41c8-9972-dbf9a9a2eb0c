import <PERSON> from 'papaparse'
import { parseString } from 'xml2js'
import ExcelJS from 'exceljs'

/**
 * CSV文件转换为XLS格式 - 使用Papa Parse
 * @param file CSV文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const csvToXlsV2 = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      encoding: 'UTF-8',
      complete: async (results) => {
        try {
          console.log('CSV解析结果:', results)
          
          if (results.errors.length > 0) {
            console.warn('CSV解析警告:', results.errors)
          }
          
          // 创建Excel工作簿
          const workbook = new ExcelJS.Workbook()
          const worksheet = workbook.addWorksheet('Sheet1')
          
          if (results.data.length > 0) {
            // 添加表头
            const headers = Object.keys(results.data[0])
            worksheet.addRow(headers)
            
            // 添加数据行
            results.data.forEach((row: any) => {
              const values = headers.map(header => row[header] || '')
              worksheet.addRow(values)
            })
            
            // 设置表头样式
            worksheet.getRow(1).font = { bold: true }
            worksheet.getRow(1).fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFE0E0E0' }
            }
          }
          
          // 转换为Buffer
          const buffer = await workbook.xlsx.writeBuffer()
          
          // 创建新的File对象
          const xlsFile = new File([buffer], file.name.replace(/\.csv$/i, '.xlsx'), {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          })
          
          console.log('CSV转换完成:', xlsFile)
          resolve(xlsFile)
        } catch (error) {
          console.error('CSV转换失败:', error)
          reject(error)
        }
      },
      error: (error) => {
        console.error('CSV解析失败:', error)
        reject(error)
      }
    })
  })
}

/**
 * XML文件转换为XLS格式 - 使用xml2js
 * @param file XML文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const xmlToXlsV2 = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const xmlData = e.target?.result as string
        console.log('原始XML数据:', xmlData)
        
        // 解析XML
        parseString(xmlData, { explicitArray: false }, async (err, result) => {
          if (err) {
            console.error('XML解析失败:', err)
            reject(err)
            return
          }
          
          console.log('XML解析结果:', result)
          
          // 将XML数据扁平化为表格数据
          const tableData = flattenXmlToTable(result)
          console.log('扁平化后的表格数据:', tableData)
          
          // 创建Excel工作簿
          const workbook = new ExcelJS.Workbook()
          const worksheet = workbook.addWorksheet('Sheet1')
          
          if (tableData.length > 0) {
            // 添加表头
            const headers = Object.keys(tableData[0])
            worksheet.addRow(headers)
            
            // 添加数据行
            tableData.forEach((row: any) => {
              const values = headers.map(header => row[header] || '')
              worksheet.addRow(values)
            })
            
            // 设置表头样式
            worksheet.getRow(1).font = { bold: true }
            worksheet.getRow(1).fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFE0E0E0' }
            }
          }
          
          try {
            // 转换为Buffer
            const buffer = await workbook.xlsx.writeBuffer()
            
            // 创建新的File对象
            const xlsFile = new File([buffer], file.name.replace(/\.xml$/i, '.xlsx'), {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })
            
            console.log('XML转换完成:', xlsFile)
            resolve(xlsFile)
          } catch (error) {
            console.error('Excel生成失败:', error)
            reject(error)
          }
        })
      } catch (error) {
        console.error('XML处理失败:', error)
        reject(error)
      }
    }
    reader.onerror = (error) => {
      console.error('文件读取失败:', error)
      reject(error)
    }
    reader.readAsText(file, 'UTF-8')
  })
}

/**
 * 将XML解析结果扁平化为表格数据
 */
function flattenXmlToTable(xmlObj: any): any[] {
  const result: any[] = []
  
  // 递归查找数组数据
  function findArrays(obj: any, path: string = ''): any[] {
    const arrays: any[] = []
    
    if (Array.isArray(obj)) {
      arrays.push({ path, data: obj })
    } else if (typeof obj === 'object' && obj !== null) {
      Object.keys(obj).forEach(key => {
        const newPath = path ? `${path}.${key}` : key
        arrays.push(...findArrays(obj[key], newPath))
      })
    }
    
    return arrays
  }
  
  const arrays = findArrays(xmlObj)
  
  if (arrays.length > 0) {
    // 使用第一个找到的数组
    const firstArray = arrays[0]
    firstArray.data.forEach((item: any, index: number) => {
      const flatItem = flattenObject(item, `${firstArray.path}[${index}]`)
      result.push(flatItem)
    })
  } else {
    // 如果没有数组，将整个对象扁平化
    result.push(flattenObject(xmlObj))
  }
  
  return result
}

/**
 * 递归扁平化对象
 */
function flattenObject(obj: any, prefix: string = ''): any {
  const flattened: any = {}
  
  Object.keys(obj).forEach(key => {
    const value = obj[key]
    const newKey = prefix ? `${prefix}.${key}` : key
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      Object.assign(flattened, flattenObject(value, newKey))
    } else if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (typeof item === 'object' && item !== null) {
          Object.assign(flattened, flattenObject(item, `${newKey}[${index}]`))
        } else {
          flattened[`${newKey}[${index}]`] = item
        }
      })
    } else {
      flattened[newKey] = value
    }
  })
  
  return flattened
}

/**
 * 读取Excel文件数据并转换为表格格式 - 使用ExcelJS
 */
export const readXlsDataV2 = async (file: File): Promise<{headers: string[], rows: any[][], totalRows: number}> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const buffer = e.target?.result as ArrayBuffer
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(buffer)
        
        // 获取第一个工作表
        const worksheet = workbook.getWorksheet(1)
        if (!worksheet) {
          resolve({ headers: [], rows: [], totalRows: 0 })
          return
        }
        
        const rows: any[][] = []
        let headers: string[] = []
        
        worksheet.eachRow((row, rowNumber) => {
          const values = row.values as any[]
          // 移除第一个空元素（ExcelJS的特性）
          const cleanValues = values.slice(1)
          
          if (rowNumber === 1) {
            headers = cleanValues.map(val => String(val || ''))
          } else {
            rows.push(cleanValues)
          }
        })
        
        // 限制显示前100行
        const limitedRows = rows.slice(0, 100)
        
        resolve({
          headers,
          rows: limitedRows,
          totalRows: rows.length
        })
      } catch (error) {
        console.error('Excel读取失败:', error)
        reject(error)
      }
    }
    reader.onerror = (error) => {
      console.error('文件读取失败:', error)
      reject(error)
    }
    reader.readAsArrayBuffer(file)
  })
}

/**
 * 检查文件是否需要格式转换
 */
export const needsConversionV2 = (fileName: string): boolean => {
  const lowerName = fileName.toLowerCase()
  return lowerName.endsWith('.csv') || lowerName.endsWith('.xml')
}

/**
 * 根据文件类型进行相应的格式转换 - V2版本
 */
export const convertFileToXlsV2 = async (file: File): Promise<File> => {
  const fileName = file.name.toLowerCase()
  
  if (fileName.endsWith('.csv')) {
    return await csvToXlsV2(file)
  } else if (fileName.endsWith('.xml')) {
    return await xmlToXlsV2(file)
  } else {
    // 如果不需要转换，直接返回原文件
    return file
  }
}
