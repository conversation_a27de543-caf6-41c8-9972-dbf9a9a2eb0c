import * as XLSX from 'xlsx'
import X2JS from 'x2js'

/**
 * CSV文件转换为XLS格式
 * @param file CSV文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const csvToXls = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const csvData = e.target?.result as string
        // 解析CSV数据
        const workbook = XLSX.read(csvData, { type: 'string' })
        // 转换为XLS格式的ArrayBuffer
        const xlsBuffer = XLSX.write(workbook, { type: 'array', bookType: 'xls' })
        // 创建新的File对象
        const xlsFile = new File([xlsBuffer], file.name.replace(/\.csv$/i, '.xls'), {
          type: 'application/vnd.ms-excel',
        })
        resolve(xlsFile)
      } catch (error) {
        console.error('CSV转换失败:', error)
        reject(error)
      }
    }
    reader.onerror = (error) => {
      console.error('文件读取失败:', error)
      reject(error)
    }
    reader.readAsText(file, 'UTF-8')
  })
}

/**
 * XML文件转换为XLS格式
 * @param file XML文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const xmlToXls = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const xmlData = e.target?.result as string
        const x2js = new X2JS()

        // 解析XML为JSON
        const jsonData = x2js.xml2js(xmlData)
        console.log('XML解析结果:', jsonData)

        // 处理XML数据结构，转换为表格数据
        let tableData: any[] = []

        // 递归函数：将嵌套对象扁平化为表格行
        const flattenObject = (obj: any, prefix = ''): any => {
          const result: any = {}
          for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
              const newKey = prefix ? `${prefix}.${key}` : key
              if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                // 如果是对象，递归处理
                Object.assign(result, flattenObject(obj[key], newKey))
              } else if (Array.isArray(obj[key])) {
                // 如果是数组，将数组元素作为单独的行
                obj[key].forEach((item: any, index: number) => {
                  if (typeof item === 'object' && item !== null) {
                    Object.assign(result, flattenObject(item, `${newKey}[${index}]`))
                  } else {
                    result[`${newKey}[${index}]`] = item
                  }
                })
              } else {
                result[newKey] = obj[key]
              }
            }
          }
          return result
        }

        if (Array.isArray(jsonData)) {
          // 如果根节点是数组，处理每个元素
          tableData = jsonData.map((item) =>
            typeof item === 'object' ? flattenObject(item) : { value: item },
          )
        } else if (typeof jsonData === 'object' && jsonData !== null) {
          // 检查是否有常见的数据容器节点
          const possibleDataKeys = ['data', 'items', 'records', 'rows', 'list']
          let foundData = false

          for (const key of Object.keys(jsonData)) {
            const value = (jsonData as any)[key]
            if (Array.isArray(value)) {
              // 找到数组数据
              tableData = value.map((item) =>
                typeof item === 'object' ? flattenObject(item) : { [key]: item },
              )
              foundData = true
              break
            }
          }

          if (!foundData) {
            // 如果没有找到数组，将整个对象扁平化为一行
            tableData = [flattenObject(jsonData)]
          }
        } else {
          // 如果是基本类型，创建简单的表格
          tableData = [{ value: jsonData }]
        }

        console.log('处理后的表格数据:', tableData)

        // 将JSON数据转换为工作表格式
        const worksheet = XLSX.utils.json_to_sheet(tableData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 转换为XLS格式
        const xlsBuffer = XLSX.write(workbook, { type: 'array', bookType: 'xls' })
        const xlsFile = new File([xlsBuffer], file.name.replace(/\.xml$/i, '.xls'), {
          type: 'application/vnd.ms-excel',
        })
        resolve(xlsFile)
      } catch (error) {
        console.error('XML转换失败:', error)
        reject(error)
      }
    }
    reader.onerror = (error) => {
      console.error('文件读取失败:', error)
      reject(error)
    }
    reader.readAsText(file, 'UTF-8')
  })
}

/**
 * 检查文件是否需要格式转换
 * @param fileName 文件名
 * @returns boolean 是否需要转换
 */
export const needsConversion = (fileName: string): boolean => {
  const lowerName = fileName.toLowerCase()
  return lowerName.endsWith('.csv') || lowerName.endsWith('.xml')
}

/**
 * 读取XLS文件数据并转换为表格格式
 * @param file XLS文件对象
 * @returns Promise<{headers: string[], rows: any[][], totalRows: number}> 表格数据
 */
export const readXlsData = async (
  file: File,
): Promise<{ headers: string[]; rows: any[][]; totalRows: number }> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = e.target?.result as ArrayBuffer
        const workbook = XLSX.read(data, { type: 'array' })

        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        // 转换为JSON数组
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        if (jsonData.length === 0) {
          resolve({ headers: [], rows: [], totalRows: 0 })
          return
        }

        // 第一行作为表头
        const headers = (jsonData[0] as any[]).map((header) => String(header || ''))
        // 其余行作为数据，限制显示前100行
        const rows = jsonData.slice(1, 101) as any[][]
        const totalRows = jsonData.length - 1

        resolve({ headers, rows, totalRows })
      } catch (error) {
        console.error('读取XLS数据失败:', error)
        reject(error)
      }
    }
    reader.onerror = (error) => {
      console.error('文件读取失败:', error)
      reject(error)
    }
    reader.readAsArrayBuffer(file)
  })
}

/**
 * 根据文件类型进行相应的格式转换
 * @param file 原始文件对象
 * @returns Promise<File> 转换后的文件对象
 */
export const convertFileToXls = async (file: File): Promise<File> => {
  const fileName = file.name.toLowerCase()

  if (fileName.endsWith('.csv')) {
    return await csvToXls(file)
  } else if (fileName.endsWith('.xml')) {
    return await xmlToXls(file)
  } else {
    // 如果不需要转换，直接返回原文件
    return file
  }
}
