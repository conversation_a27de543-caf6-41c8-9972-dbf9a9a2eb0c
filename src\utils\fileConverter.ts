import * as XLSX from 'xlsx'
import X2JS from 'x2js'

/**
 * CSV文件转换为XLS格式
 * @param file CSV文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const csvToXls = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const csvData = e.target?.result as string
        // 解析CSV数据
        const workbook = XLSX.read(csvData, { type: 'string' })
        // 转换为XLS格式的ArrayBuffer
        const xlsBuffer = XLSX.write(workbook, { type: 'array', bookType: 'xls' })
        // 创建新的File对象
        const xlsFile = new File([xlsBuffer], file.name.replace(/\.csv$/i, '.xls'), {
          type: 'application/vnd.ms-excel'
        })
        resolve(xlsFile)
      } catch (error) {
        console.error('CSV转换失败:', error)
        reject(error)
      }
    }
    reader.onerror = (error) => {
      console.error('文件读取失败:', error)
      reject(error)
    }
    reader.readAsText(file, 'UTF-8')
  })
}

/**
 * XML文件转换为XLS格式
 * @param file XML文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const xmlToXls = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const xmlData = e.target?.result as string
        const x2js = new X2JS()
        
        // 解析XML为JSON
        const jsonData = x2js.xml2js(xmlData)
        
        // 处理XML数据结构，转换为表格数据
        let tableData: any[] = []
        
        if (Array.isArray(jsonData)) {
          tableData = jsonData
        } else if (typeof jsonData === 'object' && jsonData !== null) {
          // 如果是对象，尝试找到数组数据或将对象转为数组
          const keys = Object.keys(jsonData)
          if (keys.length === 1 && Array.isArray(jsonData[keys[0]])) {
            tableData = jsonData[keys[0]]
          } else {
            // 将对象的每个属性作为一行数据
            tableData = [jsonData]
          }
        } else {
          // 如果是基本类型，创建简单的表格
          tableData = [{ value: jsonData }]
        }
        
        // 将JSON数据转换为工作表格式
        const worksheet = XLSX.utils.json_to_sheet(tableData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
        
        // 转换为XLS格式
        const xlsBuffer = XLSX.write(workbook, { type: 'array', bookType: 'xls' })
        const xlsFile = new File([xlsBuffer], file.name.replace(/\.xml$/i, '.xls'), {
          type: 'application/vnd.ms-excel'
        })
        resolve(xlsFile)
      } catch (error) {
        console.error('XML转换失败:', error)
        reject(error)
      }
    }
    reader.onerror = (error) => {
      console.error('文件读取失败:', error)
      reject(error)
    }
    reader.readAsText(file, 'UTF-8')
  })
}

/**
 * 检查文件是否需要格式转换
 * @param fileName 文件名
 * @returns boolean 是否需要转换
 */
export const needsConversion = (fileName: string): boolean => {
  const lowerName = fileName.toLowerCase()
  return lowerName.endsWith('.csv') || lowerName.endsWith('.xml')
}

/**
 * 根据文件类型进行相应的格式转换
 * @param file 原始文件对象
 * @returns Promise<File> 转换后的文件对象
 */
export const convertFileToXls = async (file: File): Promise<File> => {
  const fileName = file.name.toLowerCase()
  
  if (fileName.endsWith('.csv')) {
    return await csvToXls(file)
  } else if (fileName.endsWith('.xml')) {
    return await xmlToXls(file)
  } else {
    // 如果不需要转换，直接返回原文件
    return file
  }
}
