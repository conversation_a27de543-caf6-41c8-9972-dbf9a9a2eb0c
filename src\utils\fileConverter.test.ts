// 测试文件格式转换功能
import { csvToXls, xmlToXls, needsConversion, convertFileToXls } from './fileConverter'

// 创建测试用的CSV文件内容
const createTestCSVFile = (content: string, fileName: string = 'test.csv'): File => {
  const blob = new Blob([content], { type: 'text/csv' })
  return new File([blob], fileName, { type: 'text/csv' })
}

// 创建测试用的XML文件内容
const createTestXMLFile = (content: string, fileName: string = 'test.xml'): File => {
  const blob = new Blob([content], { type: 'text/xml' })
  return new File([blob], fileName, { type: 'text/xml' })
}

// 测试CSV转换
export const testCSVConversion = async () => {
  const csvContent = `名称,类型,描述
目录1,菜单,这是一个测试目录
目录2,数据库,这是另一个测试目录`
  
  const csvFile = createTestCSVFile(csvContent)
  
  try {
    const xlsFile = await csvToXls(csvFile)
    console.log('CSV转换成功:', {
      原文件名: csvFile.name,
      转换后文件名: xlsFile.name,
      原文件类型: csvFile.type,
      转换后文件类型: xlsFile.type,
      文件大小: xlsFile.size
    })
    return xlsFile
  } catch (error) {
    console.error('CSV转换失败:', error)
    throw error
  }
}

// 测试XML转换
export const testXMLConversion = async () => {
  const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<catalog>
  <item>
    <name>目录1</name>
    <type>菜单</type>
    <description>这是一个测试目录</description>
  </item>
  <item>
    <name>目录2</name>
    <type>数据库</type>
    <description>这是另一个测试目录</description>
  </item>
</catalog>`
  
  const xmlFile = createTestXMLFile(xmlContent)
  
  try {
    const xlsFile = await xmlToXls(xmlFile)
    console.log('XML转换成功:', {
      原文件名: xmlFile.name,
      转换后文件名: xlsFile.name,
      原文件类型: xmlFile.type,
      转换后文件类型: xlsFile.type,
      文件大小: xlsFile.size
    })
    return xlsFile
  } catch (error) {
    console.error('XML转换失败:', error)
    throw error
  }
}

// 测试needsConversion函数
export const testNeedsConversion = () => {
  const testCases = [
    { fileName: 'test.csv', expected: true },
    { fileName: 'test.xml', expected: true },
    { fileName: 'test.xls', expected: false },
    { fileName: 'test.xlsx', expected: false },
    { fileName: 'TEST.CSV', expected: true },
    { fileName: 'TEST.XML', expected: true },
  ]
  
  testCases.forEach(({ fileName, expected }) => {
    const result = needsConversion(fileName)
    console.log(`${fileName}: ${result} (期望: ${expected})`, result === expected ? '✓' : '✗')
  })
}

// 运行所有测试
export const runAllTests = async () => {
  console.log('开始测试文件格式转换功能...')
  
  console.log('\n1. 测试needsConversion函数:')
  testNeedsConversion()
  
  console.log('\n2. 测试CSV转换:')
  try {
    await testCSVConversion()
    console.log('CSV转换测试通过 ✓')
  } catch (error) {
    console.log('CSV转换测试失败 ✗')
  }
  
  console.log('\n3. 测试XML转换:')
  try {
    await testXMLConversion()
    console.log('XML转换测试通过 ✓')
  } catch (error) {
    console.log('XML转换测试失败 ✗')
  }
  
  console.log('\n测试完成!')
}

// 如果在浏览器环境中，可以在控制台运行测试
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.testFileConverter = {
    runAllTests,
    testCSVConversion,
    testXMLConversion,
    testNeedsConversion
  }
}
