<template>
  <ElDialog
    v-model="visible"
    title="数据预览"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="preview-container">
      <div class="preview-info">
        <ElAlert
          :title="`文件转换完成！共 ${totalRows} 行数据${totalRows > 100 ? '，仅显示前100行' : ''}`"
          type="success"
          :closable="false"
          show-icon
        />
      </div>
      
      <div class="preview-table" v-loading="loading">
        <ElTable
          :data="tableData"
          border
          stripe
          height="400"
          style="width: 100%"
          :empty-text="'暂无数据'"
        >
          <ElTableColumn
            v-for="(header, index) in headers"
            :key="index"
            :prop="`col_${index}`"
            :label="header || `列${index + 1}`"
            :width="120"
            show-overflow-tooltip
          />
        </ElTable>
      </div>
      
      <div class="preview-actions">
        <ElButton @click="handleCancel" size="large">
          取消上传
        </ElButton>
        <ElButton @click="handleConfirm" type="primary" size="large">
          确认上传
        </ElButton>
      </div>
    </div>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElDialog, ElTable, ElTableColumn, ElButton, ElAlert } from 'element-plus'

interface PreviewData {
  headers: string[]
  rows: any[][]
  totalRows: number
}

const visible = ref(false)
const loading = ref(false)
const previewData = ref<PreviewData>({
  headers: [],
  rows: [],
  totalRows: 0
})

// 计算属性
const headers = computed(() => previewData.value.headers)
const totalRows = computed(() => previewData.value.totalRows)

// 将二维数组转换为表格数据格式
const tableData = computed(() => {
  return previewData.value.rows.map(row => {
    const obj: Record<string, any> = {}
    row.forEach((cell, index) => {
      obj[`col_${index}`] = cell || ''
    })
    return obj
  })
})

// 事件定义
const emit = defineEmits<{
  confirm: []
  cancel: []
}>()

// 打开预览弹窗
const handleOpen = async (data: PreviewData) => {
  previewData.value = data
  visible.value = true
}

// 确认上传
const handleConfirm = () => {
  visible.value = false
  emit('confirm')
}

// 取消上传
const handleCancel = () => {
  visible.value = false
  emit('cancel')
}

// 暴露方法给父组件
defineExpose({
  handleOpen
})
</script>

<style lang="less" scoped>
.preview-container {
  .preview-info {
    margin-bottom: 16px;
  }
  
  .preview-table {
    margin-bottom: 20px;
    
    :deep(.el-table) {
      font-size: 12px;
    }
    
    :deep(.el-table th) {
      background-color: #f5f7fa;
      font-weight: bold;
    }
    
    :deep(.el-table td) {
      padding: 8px 0;
    }
  }
  
  .preview-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
  }
}
</style>
