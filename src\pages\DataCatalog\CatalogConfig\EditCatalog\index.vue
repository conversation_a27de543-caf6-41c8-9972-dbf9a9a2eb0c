<template>
  <div class="CatalogConfig">
    <div class="breadcrumb">
      <ElBreadcrumb separator="/">
        <ElBreadcrumbItem @click="router.go(-2)" class="link">数据目录</ElBreadcrumbItem>
        <ElBreadcrumbItem @click="router.go(-1)" class="link">目录配置</ElBreadcrumbItem>
        <ElBreadcrumbItem>编辑目录</ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>
    <div class="tableData">
      <MainLayout>
        <template #header>
          <div class="btns">
            <ElButton @click="AddEditDialogRef?.handleOpen(null, 0)" type="primary"
              >新建一级目录</ElButton
            >
            <ElUpload
              class="ElUpload"
              :action="importDataLakeCatalogApi()"
              :show-file-list="false"
              accept=".xls,.xlsx,.csv,.xml"
              :on-success="handleFileSuccess"
              :before-upload="beforeUpload"
              :http-request="customUpload"
              :headers="{ token: permission.token }"
            >
              <ElButton type="primary">导入目录</ElButton>
            </ElUpload>
            <ElButton :loading="btnLoading" @click="ApplyDialogRef?.handleOpen()" type="primary"
              >提交审核</ElButton
            >
            <ElButton @click="getTableData" type="primary">刷新</ElButton>
          </div>
        </template>
        <div class="table-tree">
          <ul class="custom-tree-node node-title">
            <li class="node-label">目录名称</li>
            <ul class="node-list">
              <li class="node-item" style="width: 250px">分类扩展码</li>
              <li class="node-item" style="width: 100px">目录级别</li>
              <li class="node-item" style="width: 250px">描述</li>
              <li class="node-item" style="width: 200px">创建时间</li>
              <li class="node-item" style="width: 100px">编辑标记</li>
              <li class="node-item" style="width: 100px">操作</li>
            </ul>
          </ul>
          <div class="table-tree-loading" v-loading="loading">
            <ElScrollbar height="100%">
              <ElTree
                style="box-sizing: border-box"
                :data="tableData"
                draggable
                default-expand-all
                node-key="tid"
                :props="{
                  children: 'childVOList',
                  label: 'catalogAlias',
                }"
                @node-drop="handleDrop"
              >
                <template #default="{ node, data }">
                  <ul class="custom-tree-node">
                    <li class="node-label">
                      {{ node.label }}
                    </li>
                    <ul class="node-list">
                      <li class="node-item" style="width: 250px">
                        {{ '0'.repeat(node.level) }}-{{ data.tid }}
                      </li>
                      <li class="node-item" style="width: 100px">
                        {{ data.bsType === 1 ? '菜单' : '数据库' }}
                      </li>
                      <li class="node-item" style="width: 250px">{{ data.catalogDesc }}</li>
                      <li class="node-item" style="width: 200px">{{ data.createTime }}</li>
                      <li class="node-item" style="width: 100px">
                        <ElTag v-if="data.editFlag === 2" effect="dark" type="success">新增</ElTag>
                        <ElTag v-else-if="data.editFlag === 3" effect="dark" type="warning"
                          >修改</ElTag
                        >
                        <ElTag v-else-if="data.editFlag === 4" effect="dark" type="danger"
                          >删除</ElTag
                        >
                        <ElTag v-else effect="dark" type="primary">默认</ElTag>
                      </li>
                      <li class="node-item" style="width: 130px">
                        <template v-if="![4].includes(data.editFlag)">
                          <el-button
                            @click="AddEditDialogRef?.handleOpen(data, 2)"
                            title="新建子目录"
                            class="common-icon-btn"
                            type="primary"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="CirclePlus" />
                            </template>
                          </el-button>
                          <el-button
                            @click="AddEditDialogRef?.handleOpen(data, 1)"
                            title="编辑"
                            class="common-icon-btn"
                            type="primary"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="edit" />
                            </template>
                          </el-button>
                          <el-button
                            @click="handleDelete(data)"
                            title="删除"
                            class="common-icon-btn"
                            type="danger"
                            plain
                            link
                          >
                            <template #icon>
                              <SvgIcon name="delete" />
                            </template>
                          </el-button>
                        </template>
                      </li>
                    </ul>
                  </ul>
                </template>
              </ElTree>
            </ElScrollbar>
          </div>
        </div>
        <AddEditDialog @handle-add="handleAdd" ref="AddEditDialogRef" />
        <ApplyDialog :tableData ref="ApplyDialogRef" />
      </MainLayout>
    </div>
  </div>
</template>
<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import {
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElButton,
  ElMessage,
  ElMessageBox,
  ElScrollbar,
  ElTree,
  ElUpload,
} from 'element-plus'
import { useRouter } from 'vue-router'
import usePageData from '../usePageData'
import AddEditDialog from './AddEditDialog.vue'
import { ref } from 'vue'
import ApplyDialog from './ApplyDialog.vue'
import { importDataLakeCatalogApi } from '@/api/lake_catalog'
import useUserInfo from '@/store/useUserInfo'
import { convertFileToXls, needsConversion } from '@/utils/fileConverter'

const permission = useUserInfo()

const router = useRouter()

const handleDrop = (node: any) => {
  dropUpdate(node.data)
}

const dropUpdate = (row: any) => {
  if (row.childVOList && row.childVOList.length) {
    row.childVOList.forEach((item: any) => {
      dropUpdate(item)
    })
  }
  row.editFlag = 3
  row.ptid = ''
}

const AddEditDialogRef = ref<InstanceType<typeof AddEditDialog>>()

const ApplyDialogRef = ref<InstanceType<typeof ApplyDialog>>()

const handleFileSuccess = (response: any) => {
  const { status, message, data } = response
  if ([200].includes(status)) {
    ElMessage.success('导入成功！')
    tableData.value = data
  } else {
    ElMessage.error(message)
  }
  console.log(response)
}

const beforeUpload = (rawFile: any) => {
  const allowedExtensions = ['.xls', '.xlsx', '.csv', '.xml']
  const isValidFormat = allowedExtensions.some((ext) => rawFile.name.toLowerCase().endsWith(ext))

  if (!isValidFormat) {
    ElMessage.warning('请上传xls、xlsx、csv或xml格式的文件')
    return false
  }

  return true
}

// 自定义上传函数，处理格式转换
const customUpload = async (options: any) => {
  const { file } = options
  let uploadFile = file

  try {
    // 检查是否需要格式转换
    const fileName = file.name.toLowerCase()
    if (needsConversion(fileName)) {
      if (fileName.endsWith('.csv')) {
        ElMessage.info('正在转换CSV格式...')
      } else if (fileName.endsWith('.xml')) {
        ElMessage.info('正在转换XML格式...')
      }

      uploadFile = await convertFileToXls(file)
      ElMessage.success('格式转换完成')
    }

    // 创建FormData并上传
    const formData = new FormData()
    formData.append('file', uploadFile)

    const response = await fetch(importDataLakeCatalogApi(), {
      method: 'POST',
      headers: {
        token: permission.token,
      },
      body: formData,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    handleFileSuccess(result)
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('文件上传失败，请重试')
  }
}

const handleAdd = (row: any) => {
  tableData.value.push(row)
}

const btnLoading = ref<boolean>(false)

const { tableData, getTableData, loading } = usePageData(2)

const handleDelete = async (row: any) => {
  const hasFiles = checkForFiles(row)

  ElMessageBox.confirm(
    hasFiles
      ? '当前目录下有挂载资源，是否确定删除？'
      : '删除时同步删除该目录下的资源，是否确定删除？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    handleDel(row)
  })
}

const checkForFiles = (row: any): boolean => {
  if (row.fileCount && row.fileCount > 0) {
    return true
  }

  if (row.childVOList && row.childVOList.length) {
    for (const child of row.childVOList) {
      if (checkForFiles(child)) {
        return true
      }
    }
  }

  return false
}

const handleDel = (row: any) => {
  if (row.childVOList && row.childVOList.length) {
    row.childVOList.forEach((item: any) => {
      handleDel(item)
    })
  }
  row.editFlag = 4
}
</script>
<style lang="less" scoped>
.table-tree {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .table-tree-loading {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }
}

.custom-tree-node {
  height: 40px;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .node-label {
    max-width: 280px;
    width: 100%;
    min-width: 200px;
    .ellipseLine();
  }

  .node-list {
    display: flex;
    box-sizing: border-box;
    align-items: center;
  }

  .node-item {
    line-height: 40px;
    .ellipseLine();
  }
}

.node-title {
  padding-left: 24px;
  padding-right: 30px;
}

.CatalogConfig {
  :deep(.el-tree-node__content) {
    height: 40px;
  }

  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 20px 20px 20px;

  .breadcrumb {
    height: 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    line-height: 40px;

    .link {
      :deep(.el-breadcrumb__inner) {
        color: #303133;
        font-weight: bold;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .tableData {
    padding: 15px;
    flex: 1;
    overflow: auto;
    box-sizing: border-box;

    .btns {
      display: flex;
      justify-content: flex-end;

      .ElUpload {
        margin: 0 12px;
      }
    }
  }
}
</style>
