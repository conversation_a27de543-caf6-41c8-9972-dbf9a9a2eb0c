<template>
  <div class="test-converter-v2">
    <h3>文件转换测试 V2 (使用专业库)</h3>
    
    <ElAlert 
      title="V2版本使用了更专业的库：Papa Parse (CSV) + xml2js (XML) + ExcelJS (Excel)" 
      type="info" 
      :closable="false"
      style="margin-bottom: 20px"
    />
    
    <div class="test-section">
      <h4>测试CSV转换 (Papa Parse)</h4>
      <ElButton @click="testCSVV2" type="primary">测试CSV转换 V2</ElButton>
    </div>
    
    <div class="test-section">
      <h4>测试XML转换 (xml2js)</h4>
      <ElButton @click="testXMLV2" type="primary">测试XML转换 V2</ElButton>
    </div>
    
    <div class="test-section">
      <h4>手动上传测试</h4>
      <ElUpload
        :auto-upload="false"
        :show-file-list="false"
        accept=".csv,.xml"
        :on-change="handleFileChangeV2"
      >
        <ElButton type="primary">选择文件测试 V2</ElButton>
      </ElUpload>
    </div>
    
    <div class="test-section">
      <h4>对比测试</h4>
      <ElButton @click="compareVersions" type="warning">对比V1和V2转换结果</ElButton>
    </div>
    
    <!-- 数据预览弹窗 -->
    <DataPreviewDialog 
      ref="DataPreviewDialogRef" 
      @confirm="handlePreviewConfirm" 
      @cancel="handlePreviewCancel" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElUpload, ElMessage, ElAlert } from 'element-plus'
import { 
  convertFileToXlsV2, 
  readXlsDataV2,
  csvToXlsV2,
  xmlToXlsV2 
} from '@/utils/fileConverterV2'
import { 
  convertFileToXls, 
  readXlsData 
} from '@/utils/fileConverter'
import DataPreviewDialog from './DataPreviewDialog.vue'

const DataPreviewDialogRef = ref<InstanceType<typeof DataPreviewDialog>>()

// 创建测试CSV文件
const createTestCSVFile = (): File => {
  const csvContent = `名称,类型,描述,创建时间,状态
数据目录1,菜单,这是一个测试目录,2024-01-01,启用
数据目录2,数据库,这是另一个测试目录,2024-01-02,启用
空间数据,菜单,空间数据管理,2024-01-03,禁用
业务数据,数据库,业务数据存储,2024-01-04,启用
历史数据,数据库,历史数据归档,2024-01-05,禁用`
  
  const blob = new Blob([csvContent], { type: 'text/csv' })
  return new File([blob], 'test.csv', { type: 'text/csv' })
}

// 创建测试XML文件
const createTestXMLFile = (): File => {
  const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<catalog>
  <items>
    <item>
      <name>数据目录1</name>
      <type>菜单</type>
      <description>这是一个测试目录</description>
      <createTime>2024-01-01</createTime>
      <status>启用</status>
    </item>
    <item>
      <name>数据目录2</name>
      <type>数据库</type>
      <description>这是另一个测试目录</description>
      <createTime>2024-01-02</createTime>
      <status>启用</status>
    </item>
    <item>
      <name>空间数据</name>
      <type>菜单</type>
      <description>空间数据管理</description>
      <createTime>2024-01-03</createTime>
      <status>禁用</status>
    </item>
  </items>
</catalog>`
  
  const blob = new Blob([xmlContent], { type: 'text/xml' })
  return new File([blob], 'test.xml', { type: 'text/xml' })
}

// 测试CSV转换 V2
const testCSVV2 = async () => {
  try {
    ElMessage.info('开始测试CSV转换 V2...')
    const csvFile = createTestCSVFile()
    console.log('=== CSV V2 测试开始 ===')
    console.log('原始CSV文件:', csvFile)
    
    const xlsFile = await csvToXlsV2(csvFile)
    console.log('转换后的Excel文件:', xlsFile)
    
    const previewData = await readXlsDataV2(xlsFile)
    console.log('预览数据:', previewData)
    console.log('=== CSV V2 测试结束 ===')
    
    DataPreviewDialogRef.value?.handleOpen(previewData)
    ElMessage.success('CSV V2转换测试完成')
  } catch (error) {
    console.error('CSV V2转换测试失败:', error)
    ElMessage.error('CSV V2转换测试失败')
  }
}

// 测试XML转换 V2
const testXMLV2 = async () => {
  try {
    ElMessage.info('开始测试XML转换 V2...')
    const xmlFile = createTestXMLFile()
    console.log('=== XML V2 测试开始 ===')
    console.log('原始XML文件:', xmlFile)
    
    const xlsFile = await xmlToXlsV2(xmlFile)
    console.log('转换后的Excel文件:', xlsFile)
    
    const previewData = await readXlsDataV2(xlsFile)
    console.log('预览数据:', previewData)
    console.log('=== XML V2 测试结束 ===')
    
    DataPreviewDialogRef.value?.handleOpen(previewData)
    ElMessage.success('XML V2转换测试完成')
  } catch (error) {
    console.error('XML V2转换测试失败:', error)
    ElMessage.error('XML V2转换测试失败')
  }
}

// 处理文件选择 V2
const handleFileChangeV2 = async (file: any) => {
  try {
    const rawFile = file.raw
    console.log('=== 文件上传 V2 测试开始 ===')
    console.log('选择的文件:', rawFile)
    
    ElMessage.info(`开始转换 ${rawFile.name} (V2)...`)
    const xlsFile = await convertFileToXlsV2(rawFile)
    console.log('转换后的Excel文件:', xlsFile)
    
    const previewData = await readXlsDataV2(xlsFile)
    console.log('预览数据:', previewData)
    console.log('=== 文件上传 V2 测试结束 ===')
    
    DataPreviewDialogRef.value?.handleOpen(previewData)
    ElMessage.success('文件转换完成 (V2)')
  } catch (error) {
    console.error('文件转换失败 (V2):', error)
    ElMessage.error('文件转换失败 (V2)')
  }
}

// 对比V1和V2版本
const compareVersions = async () => {
  try {
    ElMessage.info('开始对比V1和V2转换结果...')
    const csvFile = createTestCSVFile()
    
    console.log('=== 版本对比测试开始 ===')
    
    // V1版本测试
    console.log('--- V1版本测试 ---')
    const xlsFileV1 = await convertFileToXls(csvFile)
    const previewDataV1 = await readXlsData(xlsFileV1)
    console.log('V1预览数据:', previewDataV1)
    
    // V2版本测试
    console.log('--- V2版本测试 ---')
    const xlsFileV2 = await convertFileToXlsV2(csvFile)
    const previewDataV2 = await readXlsDataV2(xlsFileV2)
    console.log('V2预览数据:', previewDataV2)
    
    console.log('=== 版本对比测试结束 ===')
    
    // 显示V2的结果
    DataPreviewDialogRef.value?.handleOpen(previewDataV2)
    ElMessage.success('版本对比完成，请查看控制台')
  } catch (error) {
    console.error('版本对比失败:', error)
    ElMessage.error('版本对比失败')
  }
}

// 预览确认
const handlePreviewConfirm = () => {
  ElMessage.success('用户确认数据正确 (V2)')
}

// 预览取消
const handlePreviewCancel = () => {
  ElMessage.info('用户取消操作 (V2)')
}
</script>

<style lang="less" scoped>
.test-converter-v2 {
  padding: 20px;
  
  .test-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
    }
  }
}
</style>
