<template>
  <div class="test-converter">
    <h3>文件转换测试</h3>
    
    <div class="test-section">
      <h4>测试CSV转换</h4>
      <ElButton @click="testCSV" type="primary">测试CSV转换</ElButton>
    </div>
    
    <div class="test-section">
      <h4>测试XML转换</h4>
      <ElButton @click="testXML" type="primary">测试XML转换</ElButton>
    </div>
    
    <div class="test-section">
      <h4>手动上传测试</h4>
      <ElUpload
        :auto-upload="false"
        :show-file-list="false"
        accept=".csv,.xml"
        :on-change="handleFileChange"
      >
        <ElButton type="primary">选择文件测试</ElButton>
      </ElUpload>
    </div>
    
    <!-- 数据预览弹窗 -->
    <DataPreviewDialog 
      ref="DataPreviewDialogRef" 
      @confirm="handlePreviewConfirm" 
      @cancel="handlePreviewCancel" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElUpload, ElMessage } from 'element-plus'
import { convertFileToXls, readXlsData } from '@/utils/fileConverter'
import DataPreviewDialog from './DataPreviewDialog.vue'

const DataPreviewDialogRef = ref<InstanceType<typeof DataPreviewDialog>>()

// 创建测试CSV文件
const createTestCSVFile = (): File => {
  const csvContent = `名称,类型,描述,创建时间
数据目录1,菜单,这是一个测试目录,2024-01-01
数据目录2,数据库,这是另一个测试目录,2024-01-02
空间数据,菜单,空间数据管理,2024-01-03
业务数据,数据库,业务数据存储,2024-01-04`
  
  const blob = new Blob([csvContent], { type: 'text/csv' })
  return new File([blob], 'test.csv', { type: 'text/csv' })
}

// 创建测试XML文件
const createTestXMLFile = (): File => {
  const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<catalog>
  <item>
    <name>数据目录1</name>
    <type>菜单</type>
    <description>这是一个测试目录</description>
    <createTime>2024-01-01</createTime>
  </item>
  <item>
    <name>数据目录2</name>
    <type>数据库</type>
    <description>这是另一个测试目录</description>
    <createTime>2024-01-02</createTime>
  </item>
  <item>
    <name>空间数据</name>
    <type>菜单</type>
    <description>空间数据管理</description>
    <createTime>2024-01-03</createTime>
  </item>
</catalog>`
  
  const blob = new Blob([xmlContent], { type: 'text/xml' })
  return new File([blob], 'test.xml', { type: 'text/xml' })
}

// 测试CSV转换
const testCSV = async () => {
  try {
    ElMessage.info('开始测试CSV转换...')
    const csvFile = createTestCSVFile()
    console.log('原始CSV文件:', csvFile)
    
    const xlsFile = await convertFileToXls(csvFile)
    console.log('转换后的XLS文件:', xlsFile)
    
    const previewData = await readXlsData(xlsFile)
    console.log('预览数据:', previewData)
    
    DataPreviewDialogRef.value?.handleOpen(previewData)
    ElMessage.success('CSV转换测试完成')
  } catch (error) {
    console.error('CSV转换测试失败:', error)
    ElMessage.error('CSV转换测试失败')
  }
}

// 测试XML转换
const testXML = async () => {
  try {
    ElMessage.info('开始测试XML转换...')
    const xmlFile = createTestXMLFile()
    console.log('原始XML文件:', xmlFile)
    
    const xlsFile = await convertFileToXls(xmlFile)
    console.log('转换后的XLS文件:', xlsFile)
    
    const previewData = await readXlsData(xlsFile)
    console.log('预览数据:', previewData)
    
    DataPreviewDialogRef.value?.handleOpen(previewData)
    ElMessage.success('XML转换测试完成')
  } catch (error) {
    console.error('XML转换测试失败:', error)
    ElMessage.error('XML转换测试失败')
  }
}

// 处理文件选择
const handleFileChange = async (file: any) => {
  try {
    const rawFile = file.raw
    console.log('选择的文件:', rawFile)
    
    ElMessage.info(`开始转换 ${rawFile.name}...`)
    const xlsFile = await convertFileToXls(rawFile)
    console.log('转换后的XLS文件:', xlsFile)
    
    const previewData = await readXlsData(xlsFile)
    console.log('预览数据:', previewData)
    
    DataPreviewDialogRef.value?.handleOpen(previewData)
    ElMessage.success('文件转换完成')
  } catch (error) {
    console.error('文件转换失败:', error)
    ElMessage.error('文件转换失败')
  }
}

// 预览确认
const handlePreviewConfirm = () => {
  ElMessage.success('用户确认数据正确')
}

// 预览取消
const handlePreviewCancel = () => {
  ElMessage.info('用户取消操作')
}
</script>

<style lang="less" scoped>
.test-converter {
  padding: 20px;
  
  .test-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
    }
  }
}
</style>
